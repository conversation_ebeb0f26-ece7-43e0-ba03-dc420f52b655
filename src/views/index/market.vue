<template>
  <div class="market-container">
    <el-row :gutter="20">
      <el-col :span="24">
        <el-card shadow="hover">
          <market-date-picker />
        </el-card>
      </el-col>
      <el-col :span="24">
        <el-card shadow="hover">
          <market-indices />
        </el-card>
      </el-col>
      <el-col :span="24">
        <el-card shadow="hover">
          <template #header>
            <vab-icon icon="line-chart-line" />
            上证指数
          </template>
          <k-line-chart :interval="currentInterval" :symbol="'000001.SZ'" />
        </el-card>
      </el-col>

      <el-col :span="4">
        <el-card shadow="hover">
          <template #header>
            <vab-icon icon="shopping-bag-2-line" />
            市场热度
          </template>
          <heat-chart />
        </el-card>
      </el-col>
      <el-col :span="10">
        <el-card shadow="hover">
          <template #header>
            <vab-icon icon="shopping-bag-2-line" />
            市场热度趋势
          </template>
          <market-heat-trend />
        </el-card>
      </el-col>
      <el-col :span="10">
        <el-card shadow="hover">
          <template #header>
            <vab-icon icon="shopping-bag-2-line" />
            连板高度趋势
          </template>
          <continuous-board-trend />
        </el-card>
      </el-col>
      <el-col :span="24">
        <el-card shadow="hover">
          <market-trend-indicator />
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
  import MarketDatePicker from './components/MarketDatePicker.vue'
  import MarketIndices from './components/MarketIndices.vue'
  import MarketTrendIndicator from './components/MarketTrendIndicator.vue'
  import HeatChart from './components/HeatChart.vue'
  import MarketHeatTrend from './components/MarketHeatTrend.vue'
  import ContinuousBoardTrend from './components/ContinuousBoardTrend.vue'
  import KLineChart from './components/KLineChart.vue'

  export default {
    name: 'Market',
    components: {
      MarketDatePicker,
      MarketIndices,
      MarketTrendIndicator,
      HeatChart,
      MarketHeatTrend,
      ContinuousBoardTrend,
      KLineChart,
    },
    data() {
      return {
        currentInterval: '1d', // 默认日线
      }
    },
  }
</script>

<style lang="scss" scoped>
  .market-container {
    padding: 0 !important;
    background: $base-color-background !important;

    :deep() {
      .el-card {
        margin-bottom: 20px;
        border-radius: $base-border-radius;

        .el-card__body {
          position: relative;
          .echarts {
            width: 100%;
          }
        }
      }
    }
  }
</style>
