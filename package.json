{"name": "admin-pro", "version": "3.0.0", "private": true, "author": "github.com/zxwk1998", "scripts": {"serve": "set NODE_OPTIONS=--openssl-legacy-provider && vue-cli-service serve", "serve:mac": "export NODE_OPTIONS=--openssl-legacy-provider && vue-cli-service serve", "build": "set NODE_OPTIONS=--openssl-legacy-provider && vue-cli-service build", "build:mac": "export NODE_OPTIONS=--openssl-legacy-provider && vue-cli-service build", "lint": "vue-cli-service lint", "lint:eslint": "eslint {src,mock}/**/*.{vue,js} --fix", "lint:prettier": "prettier {src,mock}/**/*.{html,vue,css,sass,scss,js,md} --write", "lint:stylelint": "stylelint {src,mock}/**/*.{html,vue,css,sass,scss} --fix --cache --cache-location node_modules/.cache/stylelint/", "build:report": "vue-cli-service build --report", "build:deploy": "start ./deploy.sh", "build:docker": "vue-cli-service build&&docker build --pull --rm -f \"dockerfile\" -t vueadminbeautifulpro:latest \".\"&&docker run --rm -d  -p 80:80/tcp vueadminbeautifulpro:latest", "global:install": "npm install -g nrm,cnpm,npm-check-updates", "global:update": "ncu -g", "module:install": "pnpm i", "module:update": "ncu -u --reject  chalk,@logicflow/core,@logicflow/extension,screenfull,prettier,@vue/eslint-config-prettier,compression-webpack-plugin,eslint,eslint-plugin-prettier,filemanager-webpack-plugin,sass,sass-loader,webpack,vue,vuex,vue-router,@vue/cli-plugin-babel,@vue/cli-plugin-eslint,@vue/cli-plugin-pwa,@vue/cli-plugin-router,@vue/cli-plugin-vuex,@vue/cli-service,plop,vue-eslint-parser,eslint-plugin-vue,vue-i18n,vab-player,xlsx --registry=https://registry.npmmirror.com&&npm run module:install", "module:reinstall": "rimraf node_modules&&npm run module:install", "nrm:npm": "nrm use npm", "nrm:taobao": "nrm use taobao"}, "dependencies": {"@logicflow/core": "^1.0.2", "@logicflow/extension": "^1.0.2", "axios": "^1.7.9", "clipboard": "^2.0.11", "core-js": "^3.40.0", "dayjs": "^1.11.13", "disable-devtool": "^0.3.8", "echarts": "^5.6.0", "element-ui": "2.15.14", "file-saver": "^2.0.5", "js-cookie": "^3.0.5", "jsencrypt": "^3.3.2", "jsplumb": "^2.15.6", "klinecharts": "^10.0.0-alpha5", "lodash": "^4.17.21", "mockjs": "^1.1.0", "nprogress": "^0.2.0", "qs": "^6.14.0", "register-service-worker": "^1.7.2", "resize-detector": "^0.3.0", "screenfull": "5.2.0", "sortablejs": "^1.15.6", "vab-icons": "file:vab-icons", "vue": "^2.6.11", "vue-i18n": "^8.26.8", "vue-json-viewer": "^2.2.22", "vue-router": "^3.5.3", "vuedraggable": "^2.24.3", "vuex": "^3.6.2", "xlsx": "^0.17.4"}, "devDependencies": {"@vue/cli-plugin-babel": "^4.5.15", "@vue/cli-plugin-eslint": "^4.5.15", "@vue/cli-plugin-pwa": "^4.5.15", "@vue/cli-plugin-router": "^4.5.15", "@vue/cli-plugin-vuex": "^4.5.15", "@vue/cli-service": "^4.5.15", "@vue/eslint-config-prettier": "6.0.0", "body-parser": "^1.20.3", "call-rely": "^1.3.4", "chalk": "^4.1.2", "chokidar": "^4.0.3", "compression-webpack-plugin": "6.1.1", "cross-env": "^7.0.3", "eslint": "6.8.0", "eslint-plugin-prettier": "3.4.1", "eslint-plugin-vue": "^8.2.0", "filemanager-webpack-plugin": "3.1.1", "image-webpack-loader": "^8.1.0", "lint-staged": "^15.4.1", "picocolors": "^1.1.1", "postcss": "^8.5.1", "postcss-html": "^1.8.0", "postcss-jsx": "^0.36.4", "postcss-scss": "^4.0.9", "postcss-syntax": "^0.36.2", "prettier": "^2.8.8", "raw-loader": "^4.0.2", "sass": "1.32.13", "sass-loader": "10.2.0", "stylelint": "^16.13.2", "stylelint-config-prettier": "^9.0.5", "stylelint-config-recess-order": "^5.1.1", "svg-sprite-loader": "^6.0.11", "vue-eslint-parser": "^8.0.1", "webpack": "4.46.0", "webpackbar": "^7.0.0"}, "gitHooks": {"pre-commit": "lint-staged"}, "homepage": "https://vuejs-core.cn/admin-pro", "license": "Mozilla Public License Version 2.0", "lint-staged": {"*.{js,jsx,vue}": ["vue-cli-service lint", "git add"]}, "participants": ["LiufengFish"], "repository": {"type": "git", "url": "git+https://github.com/zxwk2024/admin-pro.git"}}