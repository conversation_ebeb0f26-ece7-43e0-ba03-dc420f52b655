<template>
  <div class="date-container">
    <div class="left-container">
      <div class="text-container">当前日期：</div>
      <span class="today-date">{{ today }}</span>
    </div>
    <div class="picker-container">
      <el-date-picker
        v-model="date"
        placeholder="选择日期"
        style="margin-right: 10px"
        type="date"
      />
      <el-button @click="prevDay">
        <i class="el-icon-arrow-left"></i>
        前一天
      </el-button>
      <el-button @click="nextDay">
        后一天
        <i class="el-icon-arrow-right"></i>
      </el-button>
    </div>
  </div>
</template>

<script>
  export default {
    name: 'MarketDatePicker',
    data() {
      return {
        date: '',
        today: new Date().toISOString().split('T')[0],
      }
    },
    methods: {
      prevDay() {
        const d = new Date(this.date || this.today)
        d.setDate(d.getDate() - 1)
        this.date = d.toISOString().split('T')[0]
      },
      nextDay() {
        const d = new Date(this.date || this.today)
        d.setDate(d.getDate() + 1)
        this.date = d.toISOString().split('T')[0]
      },
    },
  }
</script>

<style scoped>
  .date-container {
    display: flex;
    align-items: center;
  }

  .left-container {
    display: flex;
    align-items: center;
  }

  .text-container {
    font-size: 18px;
  }

  .picker-container {
    display: flex;
    align-items: center;
    margin-left: auto;
  }

  .today-date {
    margin-right: 10px;
    font-size: 18px;
    font-weight: bolder;
    color: #f56c6c;
  }
</style>
