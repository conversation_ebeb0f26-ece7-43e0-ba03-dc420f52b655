<template>
  <div class="k-line-chart-container">
    <div class="chart-header">
      <div class="chart-title">K线图</div>
      <div class="chart-close" @click="closeChart">
        <i class="el-icon-close"></i>
      </div>
    </div>
    <div id="chart" ref="kLineChart" class="k-line-chart"></div>
    <div class="indicators-panel">
      <!-- 显示当前可见的指标 -->
      <div v-if="indicators.MA.visible" class="indicator-item">
        <span class="indicator-name">MA</span>
        <i
          class="el-icon-close indicator-close"
          @click="closeIndicator('MA')"
        ></i>
      </div>
      <div v-if="indicators.VOL.visible" class="indicator-item">
        <span class="indicator-name">VOL</span>
        <i
          class="el-icon-close indicator-close"
          @click="closeIndicator('VOL')"
        ></i>
      </div>
      <div v-if="indicators.KDJ.visible" class="indicator-item">
        <span class="indicator-name">KDJ</span>
        <i
          class="el-icon-close indicator-close"
          @click="closeIndicator('KDJ')"
        ></i>
      </div>
      <div v-if="indicators.FISHING.visible" class="indicator-item">
        <span class="indicator-name">捕捞季节</span>
        <i
          class="el-icon-close indicator-close"
          @click="closeIndicator('FISHING')"
        ></i>
      </div>

      <!-- 添加指标按钮 -->
      <div v-if="hasHiddenIndicators" class="add-indicator-dropdown">
        <el-dropdown trigger="click" @command="openIndicator">
          <span class="add-indicator-btn">
            <i class="el-icon-plus"></i>
            添加指标
          </span>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item v-if="!indicators.MA.visible" command="MA">
              MA (移动平均线)
            </el-dropdown-item>
            <el-dropdown-item v-if="!indicators.VOL.visible" command="VOL">
              VOL (成交量)
            </el-dropdown-item>
            <el-dropdown-item v-if="!indicators.KDJ.visible" command="KDJ">
              KDJ (随机指标)
            </el-dropdown-item>
            <el-dropdown-item
              v-if="!indicators.FISHING.visible"
              command="FISHING"
            >
              捕捞季节 (副图指标)
            </el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </div>
    </div>
  </div>
</template>

<script>
  import { init, dispose } from 'klinecharts'

  export default {
    name: 'KLineChart',
    props: {
      symbol: {
        type: String,
        default: '000001.SZ',
      },
      interval: {
        type: String,
        default: '1d',
      },
    },
    data() {
      return {
        chart: null,
        data: [],
        indicators: {
          MA: { visible: true, paneId: 'candle_pane' },
          VOL: { visible: true, paneId: 'vol_pane' },
          KDJ: { visible: true, paneId: 'kdj_pane' },
          FISHING: { visible: false, paneId: 'fishing_pane' },
        },
      }
    },
    computed: {
      hasHiddenIndicators() {
        return Object.values(this.indicators).some(
          (indicator) => !indicator.visible
        )
      },
    },
    mounted() {
      this.registerFishingIndicator()
      this.initChart()
      this.loadData()
    },
    beforeDestroy() {
      if (this.chart) {
        dispose(this.chart)
      }
    },
    methods: {
      // 注册捕捞季节自定义指标
      registerFishingIndicator() {
        // 导入 klinecharts 的 registerIndicator 方法
        const { registerIndicator } = require('klinecharts')

        // 定义捕捞季节指标
        const fishingIndicator = {
          name: 'FISHING',
          shortName: '捕捞季节',
          precision: 2,
          calcParams: [], // 无需参数，使用固定算法
          figures: [
            { key: 'xys0', title: 'XYS0', type: 'bar' },
            { key: 'xys1', title: 'XYS1', type: 'line' },
            { key: 'xys2', title: 'XYS2', type: 'line' },
            { key: 'zzx', title: 'ZZX', type: 'line' },
          ],
          // 添加0轴线配置
          yAxis: {
            inside: false,
            position: 'right',
            axisLine: {
              show: true,
              color: '#989fb1',
            },
            splitLine: {
              show: true,
              color: '#989fb1',
            },
          },
          // 添加样式配置
          styles: {
            bars: [
              {
                upColor: '#ef5350', // 红色（正值）
                downColor: '#26a69a', // 绿色（负值）
                noChangeColor: '#888888', // 平盘灰色
              },
            ],
            lines: [
              { color: '#ffeb3b' }, // XYS1 黄色
              { color: '#ffffff' }, // XYS2 白色
              { color: '#ffffff' }, // ZZX 白色
            ],
          },
          calc: (dataList) => {
            const result = []

            // 辅助函数：计算EMA
            const calculateEMA = (data, period) => {
              const ema = []
              const multiplier = 2 / (period + 1)

              for (let i = 0; i < data.length; i++) {
                if (i === 0) {
                  ema[i] = data[i]
                } else {
                  ema[i] = data[i] * multiplier + ema[i - 1] * (1 - multiplier)
                }
              }
              return ema
            }

            // 辅助函数：获取前一个值（REF函数）
            const getRef = (data, index, period) => {
              const refIndex = index - period
              return refIndex >= 0 ? data[refIndex] : 0
            }

            // 计算WY1001: (2*CLOSE+HIGH+LOW)/4
            const wy1001 = dataList.map(
              (item) => (2 * item.close + item.high + item.low) / 4
            )

            // 计算WY1002: EMA(WY1001,3)
            const wy1002 = calculateEMA(wy1001, 3)

            // 计算WY1003: EMA(WY1002,3)
            const wy1003 = calculateEMA(wy1002, 3)

            // 计算WY1004: EMA(WY1003,3)
            const wy1004 = calculateEMA(wy1003, 3)

            // 计算平均价格相关数据（用于CYS13计算）
            const pjgj = dataList.map((item) =>
              item.volume > 0
                ? (item.turnover || item.volume * item.close) /
                  item.volume /
                  100
                : item.close / 100
            )
            const ssrydjx = calculateEMA(pjgj, 13)
            const ssrcjl = calculateEMA(
              dataList.map((item) => item.volume),
              13
            )
            const ssrcje = calculateEMA(
              dataList.map((item) => item.turnover || item.volume * item.close),
              13
            )
            const ssrcbjx = ssrcje.map((je, i) =>
              ssrcjl[i] > 0 ? je / ssrcjl[i] / 100 : 0
            )
            const cys13 = dataList.map((item, i) =>
              ssrcbjx[i] > 0
                ? ((item.close - ssrcbjx[i]) / ssrcbjx[i]) * 100
                : 0
            )

            // 计算换手率（假设总股本为固定值，这里简化处理）
            const capital = 100000000 // 假设总股本1亿股，实际应该从数据中获取
            const xyshsl = calculateEMA(
              dataList.map((item) => (item.volume / capital) * 100),
              13
            )

            for (let i = 0; i < dataList.length; i++) {
              const fishing = {}

              // 计算XYS0: (WY1004-REF(WY1004,1))/REF(WY1004,1)*100
              const refWy1004 = getRef(wy1004, i, 1)
              if (refWy1004 > 0) {
                fishing.xys0 = ((wy1004[i] - refWy1004) / refWy1004) * 100
              } else {
                fishing.xys0 = 0
              }

              // 计算XYS1: EMA(XYS0,2)
              if (i >= 1) {
                const xys0Values = []
                for (let j = 0; j <= i; j++) {
                  const refVal = getRef(wy1004, j, 1)
                  if (refVal > 0) {
                    xys0Values.push(((wy1004[j] - refVal) / refVal) * 100)
                  } else {
                    xys0Values.push(0)
                  }
                }
                const xys1Ema = calculateEMA(xys0Values, 2)
                fishing.xys1 = xys1Ema[i]
              } else {
                fishing.xys1 = fishing.xys0
              }

              // 计算XYS2: EMA(XYS0,1) - 实际上就是XYS0本身
              fishing.xys2 = fishing.xys0

              // ZZX: 0轴线
              fishing.zzx = 0

              result.push(fishing)
            }

            return result
          },
        }

        // 注册指标
        registerIndicator(fishingIndicator)
      },

      initChart() {
        this.chart = init(this.$refs.kLineChart, {
          styles: {
            grid: {
              show: false,
              // 网格水平线
              horizontal: {
                show: false,
                size: 1,
                color: '#989fb1',
                style: 'solid',
                dashValue: [2, 2],
              },
              // 网格垂直线
              vertical: {
                show: false,
                size: 1,
                color: '#989fb1',
                style: 'solid',
                dashValue: [2, 2],
              },
            },
            candle: {
              priceMark: {
                high: {
                  color: '#FFFFFF',
                },
                low: {
                  color: '#FFFFFF',
                },
              },
              // 蜡烛图上下间距，大于1为绝对值，大于0小余1则为比例
              margin: {
                top: 0.2,
                bottom: 0.1,
              },
              // 蜡烛图类型 'candle_solid'|'candle_stroke'|'candle_up_stroke'|'candle_down_stroke'|'ohlc'|'area'
              type: 'candle_solid',
              // 蜡烛图颜色配置：红涨绿跌
              bar: {
                upColor: '#ef5350', // 红色（上涨）
                downColor: '#26a69a', // 绿色（下跌）
                noChangeColor: '#888888', // 平盘灰色
                upBorderColor: '#ef5350', // 红色边框
                downBorderColor: '#26a69a', // 绿色边框
                noChangeBorderColor: '#888888', // 平盘灰色边框
                upWickColor: '#ef5350', // 红色芯线
                downWickColor: '#26a69a', // 绿色芯线
                noChangeWickColor: '#888888', // 平盘灰色芯线
              },
              area: {
                lineColor: '#FFFFFF',
                style: 'fill',
              },
            },
            yAxis: {
              show: true,
              axisLine: {
                show: true,
                color: '#989fb1',
                size: 1,
              },
            },
            xAxis: {
              show: true,
              axisLine: {
                show: true,
                color: '#989fb1',
                size: 1,
              },
            },
            separator: {
              size: 1,
              color: '#989fb1',
              fill: true,
            },
            // 指标样式配置：红涨绿跌
            indicator: {
              bars: [
                {
                  upColor: '#ef5350', // 红色（上涨）
                  downColor: '#26a69a', // 绿色（下跌）
                  noChangeColor: '#888888', // 平盘灰色
                },
              ],
              lines: [
                {
                  color: '#FF6B35', // K线颜色（橙红色）
                },
                {
                  color: '#4ECDC4', // D线颜色（青色）
                },
                {
                  color: '#45B7D1', // J线颜色（蓝色）
                },
                {
                  color: '#E11D74', // 第4条线颜色（粉红色）
                },
                {
                  color: '#01C5C4', // 第5条线颜色（青绿色）
                },
              ],
            },
          },
        })
        this.createIndicators()
        this.chart.setLocale('zh-CN')
        this.chart.applyNewData(this.data)
      },
      createIndicators() {
        // 创建所有可见的指标
        if (this.indicators.MA.visible) {
          this.chart.createIndicator('MA', false, {
            id: 'candle_pane',
          })
        }

        if (this.indicators.VOL.visible) {
          // 创建成交量指标，配置红涨绿跌颜色
          this.chart.createIndicator('VOL', true, {
            height: 80,
          })
        }

        if (this.indicators.KDJ.visible) {
          // 创建KDJ指标
          this.chart.createIndicator('KDJ', true, {
            height: 80,
          })
        }

        if (this.indicators.FISHING.visible) {
          // 创建捕捞季节指标
          this.chart.createIndicator('FISHING', true, {
            height: 80,
          })
        }
      },
      closeIndicator(indicatorName) {
        // 关闭指定的指标
        if (
          this.indicators[indicatorName] &&
          this.indicators[indicatorName].visible
        ) {
          // 更新指标状态为不可见
          this.$set(this.indicators[indicatorName], 'visible', false)

          // 使用 klinecharts API 移除指标，而不是重新初始化整个图表
          if (this.chart) {
            try {
              // 使用正确的 removeIndicator API
              const result = this.chart.removeIndicator({
                name: indicatorName,
              })

              if (!result) {
                console.warn(`移除指标 ${indicatorName} 失败，可能指标不存在`)
              }
            } catch (error) {
              console.warn(`移除指标 ${indicatorName} 时出错:`, error)
              // 如果 API 调用失败，回退到重新初始化的方法
              this.reinitializeChart()
            }
          }
        }
      },

      reinitializeChart() {
        // 重新初始化图表的备用方法
        if (this.chart) {
          // 保存当前数据
          const currentData = this.data

          // 销毁当前图表
          dispose(this.chart)

          // 重新初始化图表
          this.initChart()

          // 恢复数据
          if (currentData && currentData.length > 0) {
            this.chart.applyNewData(currentData)
          }
        }
      },
      async loadData() {
        try {
          // 模拟数据，实际使用时替换为真实API
          // 通过远程接口获取K线数据
          const response = await fetch(
            'https://klinecharts.com/datas/kline.json'
          )
          const remoteData = await response.json()
          this.data = remoteData
          this.chart.applyNewData(this.data)
        } catch (error) {
          console.error('加载K线数据失败:', error)
        }
      },
      closeChart() {
        // 触发关闭事件，让父组件处理关闭逻辑
        this.$emit('close')
      },

      // 添加重新打开指标的方法
      openIndicator(indicatorName) {
        if (
          this.indicators[indicatorName] &&
          !this.indicators[indicatorName].visible
        ) {
          // 更新指标状态为可见
          this.$set(this.indicators[indicatorName], 'visible', true)

          // 重新创建指标
          if (this.chart) {
            try {
              if (indicatorName === 'MA') {
                this.chart.createIndicator('MA', false, {
                  id: 'candle_pane',
                })
              } else if (indicatorName === 'VOL') {
                // 重新创建成交量指标，使用全局样式配置
                this.chart.createIndicator('VOL', true, {
                  height: 80,
                })
              } else if (indicatorName === 'KDJ') {
                // 重新创建KDJ指标，使用全局样式配置
                this.chart.createIndicator('KDJ', true, {
                  height: 80,
                })
              } else if (indicatorName === 'FISHING') {
                // 重新创建捕捞季节指标，使用全局样式配置
                this.chart.createIndicator('FISHING', true, {
                  height: 80,
                })
              }
            } catch (error) {
              console.warn(`创建指标 ${indicatorName} 时出错:`, error)
            }
          }
        }
      },

      // 获取指标状态
      getIndicatorStatus() {
        return Object.keys(this.indicators).reduce((status, key) => {
          status[key] = this.indicators[key].visible
          return status
        }, {})
      },
    },
  }
</script>

<style lang="scss" scoped>
  .k-line-chart-container {
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 500px; /* 高度增加一倍 */
    background-color: #fff;
    border-radius: 4px;

    .chart-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 8px 12px;
      border-bottom: 1px solid #eee;

      .chart-title {
        font-size: 14px;
        font-weight: 500;
      }

      .chart-close {
        color: #909399;
        cursor: pointer;
        &:hover {
          color: #409eff;
        }
      }
    }

    .k-line-chart {
      flex: 1;
      width: 100%;
    }

    .indicators-panel {
      display: flex;
      flex-wrap: wrap;
      padding: 8px;
      border-top: 1px solid #eee;

      .indicator-item {
        display: flex;
        align-items: center;
        padding: 4px 8px;
        margin-right: 12px;
        background-color: #f5f7fa;
        border-radius: 4px;

        .indicator-name {
          margin-right: 4px;
          font-size: 12px;
        }

        .indicator-close {
          font-size: 12px;
          color: #909399;
          cursor: pointer;
          &:hover {
            color: #f56c6c;
          }
        }
      }

      .add-indicator-dropdown {
        .add-indicator-btn {
          display: flex;
          align-items: center;
          padding: 4px 8px;
          font-size: 12px;
          color: #409eff;
          cursor: pointer;
          background-color: #e6f7ff;
          border: 1px dashed #409eff;
          border-radius: 4px;
          transition: all 0.3s;

          i {
            margin-right: 4px;
          }

          &:hover {
            color: #fff;
            background-color: #409eff;
            border-color: #409eff;
          }
        }
      }
    }
  }
</style>
