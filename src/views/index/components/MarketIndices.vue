<template>
  <el-row :gutter="20">
    <el-col v-for="(item, index) in indices" :key="index" :span="4">
      <el-card shadow="hover">
        <div class="index-header">
          <i class="index-icon" :class="item.icon"></i>
          <div class="index-name">{{ item.name }}</div>
        </div>
        <div
          class="index-value"
          :style="{ color: item.isUp ? '#F56C6C' : '#67C23A' }"
        >
          <div class="value-container">
            <div class="price-container">
              {{ item.value }}
              <span class="index-change">{{ item.change }}</span>
            </div>
            <span v-if="item.trend" class="index-trend">
              趋势:
              <span class="trend-value">{{ item.trend }}</span>
            </span>
          </div>
        </div>
      </el-card>
    </el-col>
  </el-row>
</template>

<script>
  export default {
    name: 'MarketIndices',
    data() {
      return {
        indices: [
          {
            name: '上证指数',
            value: '3254.78',
            change: '+1.23%',
            isUp: true,
            icon: 'el-icon-s-data',
          },
          {
            name: '平均股价',
            value: '15.67',
            change: '-0.45%',
            isUp: false,
            icon: 'el-icon-s-opportunity',
            trend: '多头',
          },
          {
            name: '深圳指数',
            value: '11234.56',
            change: '+0.78%',
            isUp: true,
            icon: 'el-icon-s-marketing',
          },
          {
            name: '创业板值',
            value: '2345.67',
            change: '-1.23%',
            isUp: false,
            icon: 'el-icon-s-flag',
          },
          {
            name: '科创板指',
            value: '987.65',
            change: '+2.34%',
            isUp: true,
            icon: 'el-icon-s-platform',
          },
          {
            name: '北交板值',
            value: '876.54',
            change: '-0.67%',
            isUp: false,
            icon: 'el-icon-s-order',
          },
        ],
      }
    },
  }
</script>

<style lang="scss" scoped>
  .index-header {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
  }

  .index-icon {
    margin-right: 8px;
    font-size: 18px;
  }

  .index-name {
    font-size: 14px;
    color: #606266;
  }

  .index-value {
    font-size: 18px;
    font-weight: bold;

    .value-container {
      display: flex;
      align-items: center;
      justify-content: space-between;
    }

    .price-container {
      display: flex;
      align-items: center;
    }
  }

  .index-change {
    font-size: 14px;
    font-weight: normal;
  }

  .index-trend {
    font-size: 16px;
    color: #909399;
  }

  .trend-value {
    color: #f56c6c;
  }
</style>
