<template>
  <div class="market-heat-trend">
    <vab-chart
      :init-options="initOptions"
      :option="option"
      theme="vab-echarts-theme"
    />
  </div>
</template>

<script>
  import VabChart from '@/extra/VabChart'
  import _ from 'lodash'

  export default {
    name: 'MarketHeatTrend',
    components: {
      VabChart,
    },
    data() {
      return {
        initOptions: {
          renderer: 'svg',
        },
        option: {
          tooltip: {
            trigger: 'axis',
            extraCssText: 'z-index:1',
          },
          grid: {
            top: '1%',
            left: '2%',
            right: '4%',
            bottom: '52%',
            containLabel: true,
          },
          xAxis: [
            {
              type: 'category',
              boundaryGap: false,
              data: [],
              axisLine: {
                lineStyle: {
                  color: '#DCE2E8',
                },
              },
              axisTick: {
                show: false,
              },
              axisLabel: {
                interval: 4,
                color: '#556677',
                fontSize: 12,
                margin: 15,
                formatter: function (value) {
                  return value
                },
                name: '天',
                nameLocation: 'end',
              },
            },
          ],
          yAxis: [
            {
              type: 'value',
              min: 0,
              max: 100,
              axisTick: {
                show: true,
              },
              axisLine: {
                show: true,
                lineStyle: {
                  color: '#DCE2E8',
                },
              },
              axisLabel: {
                color: '#556677',
                formatter: '{value}',
              },
              splitLine: {
                show: false,
              },
            },
          ],
          series: [
            {
              name: '市场热度',
              type: 'line',
              data: [],
              smooth: false,
              symbol: 'circle',
              symbolSize: 6,
              showSymbol: false,
              lineStyle: {
                width: 3,
                color: new VabChart.graphic.LinearGradient(0, 0, 1, 0, [
                  {
                    offset: 0,
                    color: '#FF6B6B',
                  },
                  {
                    offset: 1,
                    color: '#FF8E53',
                  },
                ]),
              },
              areaStyle: {
                opacity: 0.3,
                color: new VabChart.graphic.LinearGradient(0, 0, 0, 1, [
                  {
                    offset: 0,
                    color: 'rgba(255, 107, 107, 0.3)',
                  },
                  {
                    offset: 1,
                    color: 'rgba(255, 142, 83, 0.1)',
                  },
                ]),
              },
            },
          ],
        },
      }
    },
    mounted() {
      this.generateData()
    },
    methods: {
      generateData() {
        const dates = []
        const data = []
        const now = new Date()

        // 生成最近30天的数据
        for (let i = 29; i >= 0; i--) {
          const date = new Date(now)
          date.setDate(now.getDate() - i)
          dates.push(`${date.getMonth() + 1}/${date.getDate()}`)
          data.push(_.random(30, 90)) // 生成30-90之间的随机热度值
        }

        this.option.xAxis[0].data = dates
        this.option.series[0].data = data
      },
    },
  }
</script>

<style lang="scss" scoped>
  .market-heat-trend {
    box-sizing: border-box;
    width: 100%;
    height: 200px;
    padding: 5px 0;
  }

  :deep() {
    .echart {
      width: 1700px;
    }
  }
</style>
