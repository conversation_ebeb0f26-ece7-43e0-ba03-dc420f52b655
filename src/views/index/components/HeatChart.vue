<template>
  <div class="heat-chart-container">
    <div ref="chart" style="width: 100%; height: 200px"></div>
  </div>
</template>

<script>
  import * as echarts from 'echarts'

  export default {
    name: 'HeatChart',
    mounted() {
      this.initChart()
    },
    methods: {
      initChart() {
        const chartDom = this.$refs.chart
        const myChart = echarts.init(chartDom)

        const option = {
          // 图表标题配置
          title: {
            text: '市场热度', // 标题文本
            left: 'center', // 标题水平位置
            bottom: '0%', // 标题垂直位置
            textStyle: {
              fontSize: 24, // 标题字体大小
              fontWeight: 'bolder', // 标题字体粗细
              color: '#ff8800', // 标题颜色
            },
          },
          // 系列列表
          series: [
            {
              type: 'gauge',
              radius: '90%', // 将半径从80%增加到90%
              axisLine: {
                lineStyle: {
                  width: 10,
                  color: [
                    [0.3, '#67e0e3'],
                    [0.7, '#37a2da'],
                    [1, '#fd666d'],
                  ],
                },
              },
              pointer: {
                itemStyle: {
                  color: 'auto',
                },
              },
              axisTick: {
                distance: -15,
                length: 10,
                lineStyle: {
                  color: '#fff',
                  width: 1,
                },
              },
              splitLine: {
                distance: -120,
                length: 30,
                lineStyle: {
                  color: '#fff',
                  width: 1,
                },
              },
              axisLabel: {
                color: 'inherit',
                distance: 105,
                fontSize: 10,
              },
              detail: {
                valueAnimation: true,
                formatter: '{value}',
                color: 'inherit',
              },
              data: [
                {
                  value: 90,
                },
              ],
            },
          ],
        }

        myChart.setOption(option)
      },
    },
  }
</script>

<style lang="scss" scoped>
  .heat-chart-container {
    width: 100%;
    height: 100%;
  }
</style>
