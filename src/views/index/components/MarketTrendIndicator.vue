<template>
  <el-col :span="12">
    <div class="market-trend-container">
      <el-tabs v-model="activeTab" type="card">
        <el-tab-pane label="涨停池" name="limitUp">
          <el-table :data="limitUpData" style="width: 100%">
            <el-table-column label="股票/代码" prop="stock" width="120">
              <template #default="{ row }">
                <div style="line-height: 1.2">
                  <div>{{ row.stock.split(' ')[0] }}</div>
                  <div style="font-size: 10px; color: #999">
                    {{ row.stock.split(' ')[1] }}
                  </div>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="现价" prop="price" sortable>
              <template #default="{ row }">
                <span style="color: red">{{ row.price }}</span>
              </template>
            </el-table-column>
            <el-table-column label="涨跌幅" prop="change" sortable>
              <template #default="{ row }">
                <span style="color: red">{{ row.change }}</span>
              </template>
            </el-table-column>
            <el-table-column label="连板数" prop="days" sortable />
            <el-table-column label="动因" prop="reason" width="380" />
          </el-table>
        </el-tab-pane>
        <el-tab-pane label="连板池" name="continuousLimitUp">
          <el-table :data="continuousLimitUpData" style="width: 100%">
            <el-table-column label="股票/代码" prop="stock" width="180">
              <template #default="{ row }">
                <div style="line-height: 1.2">
                  <div>{{ row.stock.split(' ')[0] }}</div>
                  <div style="font-size: 10px; color: #999">
                    {{ row.stock.split(' ')[1] }}
                  </div>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="现价" prop="price" sortable>
              <template #default="{ row }">
                <span style="color: red">{{ row.price }}</span>
              </template>
            </el-table-column>
            <el-table-column label="涨跌幅" prop="change" sortable>
              <template #default="{ row }">
                <span style="color: red">{{ row.change }}</span>
              </template>
            </el-table-column>
            <el-table-column label="连续涨停数" prop="days" sortable />
            <el-table-column label="动因" prop="reason" />
          </el-table>
        </el-tab-pane>
        <el-tab-pane label="炸板池" name="failedLimitUp">
          <el-table :data="failedLimitUpData" style="width: 100%">
            <el-table-column label="股票/代码" prop="stock" width="180">
              <template #default="{ row }">
                <div style="line-height: 1.2">
                  <div>{{ row.stock.split(' ')[0] }}</div>
                  <div style="font-size: 10px; color: #999">
                    {{ row.stock.split(' ')[1] }}
                  </div>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="现价" prop="price" sortable>
              <template #default="{ row }">
                <span style="color: red">{{ row.price }}</span>
              </template>
            </el-table-column>
            <el-table-column label="涨跌幅" prop="change" sortable>
              <template #default="{ row }">
                <span style="color: red">{{ row.change }}</span>
              </template>
            </el-table-column>
            <el-table-column label="连续涨停数" prop="days" sortable />
            <el-table-column label="动因" prop="reason" />
          </el-table>
        </el-tab-pane>
        <el-tab-pane label="跌停池" name="limitDown">
          <el-table :data="limitDownData" style="width: 100%">
            <el-table-column label="股票/代码" prop="stock" width="180">
              <template #default="{ row }">
                <div style="line-height: 1.2">
                  <div>{{ row.stock.split(' ')[0] }}</div>
                  <div style="font-size: 10px; color: #999">
                    {{ row.stock.split(' ')[1] }}
                  </div>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="现价" prop="price" sortable>
              <template #default="{ row }">
                <span style="color: red">{{ row.price }}</span>
              </template>
            </el-table-column>
            <el-table-column label="涨跌幅" prop="change" sortable>
              <template #default="{ row }">
                <span style="color: red">{{ row.change }}</span>
              </template>
            </el-table-column>
            <el-table-column label="连续涨停数" prop="days" sortable />
            <el-table-column label="动因" prop="reason" />
          </el-table>
        </el-tab-pane>
      </el-tabs>
    </div>
  </el-col>
</template>

<script>
  export default {
    name: 'MarketTrendIndicator',
    data() {
      return {
        activeTab: 'limitUp',
        limitUpData: [
          {
            stock: '泛微网络 sh603039',
            price: '68.54',
            change: '+10.00%',
            days: '1',
            reason:
              'AI智能体|泛微数智大脑Xiaoe.AI为组织每个用户提供了一位7*24工作的智能助理。',
          },
          {
            stock: '亚联机械 sz001395',
            price: '51.68',
            change: '+10.00%',
            days: '3',
            reason:
              '次新+家居|公司是人造板生产装备整体解决方案的供应商和服务商。',
          },
        ],
        continuousLimitUpData: [],
        failedLimitUpData: [],
        limitDownData: [],
      }
    },
  }
</script>

<style lang="scss" scoped>
  .market-trend-container {
    font-size: 12px;

    :deep() {
      .el-tabs__item {
        height: 32px;
        line-height: 32px;
      }

      .el-table td .cell {
        font-size: 12px;
      }
    }
  }
</style>
