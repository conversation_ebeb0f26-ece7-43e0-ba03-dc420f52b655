<template>
  <div class="continuous-board-trend">
    <vab-chart
      :init-options="initOptions"
      :option="option"
      theme="vab-echarts-theme"
    />
  </div>
</template>

<script>
  import VabChart from '@/extra/VabChart'
  import _ from 'lodash'

  export default {
    name: 'ContinuousBoardTrend',
    components: {
      VabChart,
    },
    data() {
      return {
        initOptions: {
          renderer: 'svg',
        },
        option: {
          tooltip: {
            trigger: 'axis',
            extraCssText: 'z-index:1',
          },
          grid: {
            top: '1%',
            left: '2%',
            right: '4%',
            bottom: '52%',
            containLabel: true,
          },
          xAxis: [
            {
              type: 'category',
              boundaryGap: true,
              data: [],
              axisLine: {
                show: false,
                lineStyle: {
                  color: '#DCE2E8',
                },
              },
              axisTick: {
                show: false,
              },
              splitLine: {
                show: false,
              },
              axisLabel: {
                interval: 4,
                color: '#556677',
                fontSize: 12,
                margin: 15,
                formatter: function (value) {
                  return value
                },
                name: '天',
                nameLocation: 'end',
              },
            },
          ],
          yAxis: [
            {
              type: 'value',
              min: 0,
              max: 10,
              axisTick: {
                show: true,
              },
              axisLine: {
                show: true,
                lineStyle: {
                  color: '#DCE2E8',
                },
              },
              axisLabel: {
                color: '#556677',
                formatter: '{value}',
              },
              splitLine: {
                show: true,
                lineStyle: {
                  color: '#DCE2E8',
                  type: 'dashed',
                },
              },
            },
          ],
          series: [
            {
              name: '连板高度',
              type: 'pictorialBar',
              symbol:
                'path://M0,10 L10,10 C5.5,10 5.5,5 5,0 C4.5,5 4.5,10 0,10 z',
              itemStyle: {
                color: new VabChart.graphic.LinearGradient(0, 0, 0, 1, [
                  {
                    offset: 0,
                    color: '#1890FF',
                  },
                  {
                    offset: 1,
                    color: 'rgba(24, 144, 255, 0.3)',
                  },
                ]),
              },
              emphasis: {
                itemStyle: {
                  color: new VabChart.graphic.LinearGradient(0, 0, 0, 1, [
                    {
                      offset: 0,
                      color: '#40A9FF',
                    },
                    {
                      offset: 1,
                      color: 'rgba(64, 169, 255, 0.3)',
                    },
                  ]),
                },
              },
              label: {
                show: true,
                position: 'top',
                formatter: '{c}板',
                color: '#1890FF',
                fontSize: 12,
                fontWeight: 'bold',
              },
              data: [],
              z: 12,
            },
          ],
        },
      }
    },
    mounted() {
      this.generateData()
    },
    methods: {
      generateData() {
        const dates = []
        const data = []
        const now = new Date()

        // 生成最近20天的数据
        for (let i = 19; i >= 0; i--) {
          const date = new Date(now)
          date.setDate(now.getDate() - i)
          dates.push(`${date.getMonth() + 1}/${date.getDate()}`)
          data.push(_.random(1, 10)) // 生成1-10之间的随机连板高度值
        }

        this.option.xAxis[0].data = dates
        this.option.series[0].data = data
      },
    },
  }
</script>

<style lang="scss" scoped>
  .continuous-board-trend {
    box-sizing: border-box;
    width: 100%;
    height: 200px;
    padding: 5px 0;
  }

  :deep() {
    .echart {
      width: 1700px;
    }
  }
</style>
